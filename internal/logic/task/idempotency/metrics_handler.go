package idempotency

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/gogf/gf/v2/os/glog"
)

// MetricsHandler provides methods to expose idempotency metrics
type MetricsHandler struct{}

// NewMetricsHandler creates a new metrics handler
func NewMetricsHandler() *MetricsHandler {
	return &MetricsHandler{}
}

// GetMetricsJSON returns metrics as JSON string
func (h *MetricsHandler) GetMetricsJSON() (string, error) {
	stats := GlobalMetrics.GetStats()
	data, err := json.MarshalIndent(stats, "", "  ")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// GetMetricsPrometheus returns metrics in Prometheus format
func (h *MetricsHandler) GetMetricsPrometheus() string {
	stats := GlobalMetrics.GetStats()
	
	return fmt.Sprintf(`# HELP idempotency_lock_acquired_total Total number of successfully acquired locks
# TYPE idempotency_lock_acquired_total counter
idempotency_lock_acquired_total %d

# HELP idempotency_lock_rejected_total Total number of rejected lock attempts (duplicate prevention)
# TYPE idempotency_lock_rejected_total counter
idempotency_lock_rejected_total %d

# HELP idempotency_redis_error_total Total number of Redis errors during lock operations
# TYPE idempotency_redis_error_total counter
idempotency_redis_error_total %d

# HELP idempotency_active_locks_count Current number of active locks
# TYPE idempotency_active_locks_count gauge
idempotency_active_locks_count %d

# HELP idempotency_effectiveness_rate Percentage of requests rejected due to idempotency
# TYPE idempotency_effectiveness_rate gauge
idempotency_effectiveness_rate %.2f
`,
		stats.LockAcquiredTotal,
		stats.LockRejectedTotal,
		stats.RedisErrorTotal,
		stats.ActiveLocksCount,
		stats.GetEffectiveRate(),
	)
}

// LogMetrics logs current metrics to the application log
func (h *MetricsHandler) LogMetrics(ctx context.Context) {
	stats := GlobalMetrics.GetStats()
	
	glog.Infof(ctx, "[IdempotencyMetrics] Locks acquired: %d, Locks rejected: %d (%.2f%% effectiveness), Redis errors: %d, Active locks: %d",
		stats.LockAcquiredTotal,
		stats.LockRejectedTotal,
		stats.GetEffectiveRate(),
		stats.RedisErrorTotal,
		stats.ActiveLocksCount,
	)
}
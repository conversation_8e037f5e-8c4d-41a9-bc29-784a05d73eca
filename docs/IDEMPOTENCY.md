# Withdrawal Order Idempotency Control Module

## Overview

The Idempotency Control Module provides a lightweight, Redis-based distributed locking mechanism to prevent duplicate processing of withdrawal orders. This ensures that each transaction is processed exactly once, even in scenarios involving service failures, network retries, or message queue duplicate consumption.

## Architecture

### Core Components

1. **Locker Interface** (`internal/logic/task/idempotency/locker.go`)
   - Defines the contract for distributed lock implementations
   - Provides lock key generation utilities

2. **Redis Locker** (`internal/logic/task/idempotency/redis_locker.go`)
   - Implements the Locker interface using Redis
   - Uses atomic `SET NX EX` command for lock acquisition
   - Handles TTL management and degraded mode operation

3. **Configuration** (`internal/logic/task/idempotency/config.go`)
   - Manages idempotency settings
   - Provides default configurations
   - Supports environment-specific configurations

4. **Metrics** (`internal/logic/task/idempotency/metrics.go`)
   - Tracks lock operations for monitoring
   - Provides insights into duplicate prevention effectiveness

## How It Works

### Lock Acquisition Flow

```
1. Message received from queue
2. Parse transaction details
3. Generate unique lock key: idempotency:lock:{type}:{id}
4. Attempt to acquire Redis lock with SET NX EX
5. If lock acquired:
   - Process the transaction
   - Lock auto-expires after TTL
6. If lock exists:
   - Log duplicate attempt
   - Discard message (no DLQ)
7. If Redis error:
   - Log error
   - Process anyway (degraded mode)
```

### Lock Key Format

```
idempotency:lock:{transaction_type}:{transaction_id}
```

Examples:
- `idempotency:lock:user_withdrawal:123456`
- `idempotency:lock:merchant_withdrawal:MW-789`
- `idempotency:lock:merchant_settlement:999888`

### TTL Strategy

The lock TTL (Time-To-Live) is crucial for system reliability:

- **Default**: 1 hour (3600 seconds)
- **Purpose**: Prevents permanent locks from crashed workers
- **No manual release**: Locks expire naturally via TTL
- **Configuration**: Adjustable based on processing requirements

## Configuration

### Basic Configuration

```yaml
withdrawalProcessor:
  idempotency:
    enabled: true        # Enable/disable idempotency checking
    lockTtl: 3600       # Lock TTL in seconds
    redisTimeout: 5000  # Redis operation timeout in milliseconds
```

### Environment-Specific Settings

**Development** (Fast iteration):
```yaml
idempotency:
  enabled: true
  lockTtl: 300        # 5 minutes
  redisTimeout: 3000  # 3 seconds
```

**Production** (High reliability):
```yaml
idempotency:
  enabled: true
  lockTtl: 7200       # 2 hours
  redisTimeout: 10000 # 10 seconds
```

## Integration Points

### 1. Withdrawal Consumer (`withdrawal_consumer/processor.go`)

```go
// Idempotency check added at the beginning of ProcessMessage
if processorCfg.Idempotency.Enabled {
    locker := idempotency.NewRedisLocker(...)
    lockKey := idempotency.GenerateLockKey(
        idempotency.TransactionTypeUserWithdrawal,
        withdrawal.UserWithdrawsId,
    )
    
    lockAcquired, err := locker.TryLock(ctx, lockKey, ttl)
    if !lockAcquired {
        // Duplicate detected - skip processing
        return
    }
}
```

### 2. Enhanced Consumer (`enhanced_consumer/processor.go`)

Handles multiple transaction types with type-aware lock generation:
- User Withdrawals
- Merchant Withdrawals
- Merchant Settlements

## Monitoring & Metrics

### Available Metrics

1. **idempotency_lock_acquired_total**
   - Counter: Successfully acquired locks
   - Indicates new transactions being processed

2. **idempotency_lock_rejected_total**
   - Counter: Rejected lock attempts
   - **Key metric**: Shows duplicates prevented

3. **idempotency_redis_error_total**
   - Counter: Redis operation failures
   - Indicates infrastructure issues

4. **idempotency_effectiveness_rate**
   - Gauge: Percentage of rejected attempts
   - Measures duplicate prevention effectiveness

### Metrics Access

```go
// Get metrics programmatically
stats := idempotency.GlobalMetrics.GetStats()

// Export as Prometheus format
handler := idempotency.NewMetricsHandler()
promMetrics := handler.GetMetricsPrometheus()

// Log metrics
handler.LogMetrics(ctx)
```

## Error Handling & Degraded Mode

### Redis Connection Failures

When Redis is unavailable:
1. Log error with severity
2. Continue processing (degraded mode)
3. Increment error metrics
4. Alert monitoring systems

### Recovery Strategies

1. **Automatic retry**: Built into Redis client
2. **Circuit breaker**: Prevent cascading failures
3. **Fallback**: Process without idempotency (logged)
4. **Manual intervention**: For persistent issues

## Testing

### Unit Tests

Run the comprehensive test suite:
```bash
go test ./internal/logic/task/idempotency/...
```

### Test Coverage

- Lock key generation
- Redis lock acquisition
- Disabled mode behavior
- Configuration handling
- Metrics tracking
- Error scenarios

### Integration Testing

Test with actual Redis:
```go
// Example integration test
func TestIdempotencyWithRedis(t *testing.T) {
    // Connect to test Redis
    redis := setupTestRedis()
    locker := idempotency.NewRedisLocker(redis, true, time.Hour)
    
    // Test concurrent lock attempts
    key := "test:withdrawal:123"
    
    // First attempt should succeed
    acquired1, _ := locker.TryLock(ctx, key, time.Minute)
    assert.True(t, acquired1)
    
    // Second attempt should fail (duplicate)
    acquired2, _ := locker.TryLock(ctx, key, time.Minute)
    assert.False(t, acquired2)
}
```

## Best Practices

### 1. TTL Configuration

- Set TTL > maximum processing time
- Consider network delays and retries
- Add buffer for unexpected delays
- Monitor actual processing times

### 2. Lock Key Design

- Include transaction type for clarity
- Use unique, immutable IDs
- Avoid user-controllable values
- Consider namespacing for multi-tenant

### 3. Monitoring

- Set up alerts for high rejection rates
- Monitor Redis connection health
- Track processing time vs TTL
- Review effectiveness metrics regularly

### 4. Operational Considerations

- Plan for Redis maintenance windows
- Document degraded mode behavior
- Establish runbooks for common issues
- Regular backup of Redis data

## Troubleshooting

### Common Issues

**Issue**: High rejection rate
- **Cause**: Message queue delivering duplicates
- **Solution**: Check queue configuration, increase visibility timeout

**Issue**: Redis connection timeouts
- **Cause**: Network issues or Redis overload
- **Solution**: Increase timeout, scale Redis, check network

**Issue**: Locks not expiring
- **Cause**: TTL too long or Redis persistence issue
- **Solution**: Verify TTL configuration, check Redis logs

### Debug Commands

```bash
# Check if lock exists
redis-cli GET "idempotency:lock:user_withdrawal:123456"

# Monitor lock operations
redis-cli MONITOR | grep idempotency

# Check Redis performance
redis-cli INFO stats

# List all idempotency locks
redis-cli KEYS "idempotency:lock:*"
```

## Performance Considerations

### Redis Operations

- **SET NX EX**: O(1) time complexity
- **Network latency**: ~1-5ms typical
- **Throughput**: 10,000+ ops/sec per Redis instance

### Optimization Tips

1. Use Redis connection pooling
2. Consider Redis Cluster for scale
3. Implement local caching for read-heavy scenarios
4. Monitor and tune timeout values

## Future Enhancements

### Planned Features

1. **Lock renewal**: Extend TTL for long-running tasks
2. **Lock owner tracking**: Store worker ID in lock value
3. **Automatic cleanup**: Remove expired locks proactively
4. **Multi-region support**: Cross-region lock coordination

### Potential Improvements

1. Support for other backends (etcd, Consul)
2. Lock queuing mechanism
3. Priority-based lock acquisition
4. Distributed tracing integration

## Conclusion

The Idempotency Control Module provides robust duplicate prevention for withdrawal processing with minimal overhead. By leveraging Redis's atomic operations and implementing proper TTL management, the system ensures exactly-once processing semantics while maintaining high availability and performance.
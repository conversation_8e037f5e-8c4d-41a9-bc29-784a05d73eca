{"withdrawalProcessor": {"enabled": true, "spec": "*/10 * * * * *", "batchSize": 10, "idempotency": {"enabled": true, "lockTtl": 3600, "redisTimeout": 5000}, "wallets": {"ETH": {"privateKeyEnvVar": "ETH_HOT_WALLET_PRIVATE_KEY", "address": "0x..."}, "TRON": {"privateKeyEnvVar": "TRON_HOT_WALLET_PRIVATE_KEY", "address": "T..."}}, "rpc": {"ETH": {"url": "https://mainnet.infura.io/v3/YOUR_API_KEY", "apiKey": "YOUR_API_KEY", "chainId": 1}, "TRON": {"url": "https://api.trongrid.io", "apiKey": "YOUR_API_KEY", "chainId": 0}}, "retry": {"maxAttempts": 3, "delaySeconds": 30, "nonRetryableErrors": ["insufficient_balance", "invalid_address"]}, "monitoring": {"lowBalanceThreshold": {"ETH": "0.1", "USDT": "100"}, "alertWebhookUrl": "https://hooks.slack.com/services/YOUR_WEBHOOK", "alertOnFailure": true, "alertOnLowBalance": true, "alertOnLimitExceeded": true}, "enabledTokens": {"USDT_ERC20": true, "USDT_TRC20": true}, "tokenContracts": {"USDT_ERC20": "******************************************", "USDT_TRC20": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"}, "tokenPrecisions": {"USDT_ERC20": 6, "USDT_TRC20": 6}}}
package idempotency_test

import (
	"context"
	"testing"
	"time"

	"task-withdraw/internal/logic/task/idempotency"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRedisService is a mock implementation of service.IRedis for testing
type MockRedisService struct {
	mock.Mock
}

func (m *MockRedisService) Client(name ...string) *gredis.Redis {
	args := m.Called(name)
	return args.Get(0).(*gredis.Redis)
}

func (m *MockRedisService) Set(ctx context.Context, key string, value interface{}, duration time.Duration, name ...string) error {
	args := m.Called(ctx, key, value, duration, name)
	return args.Error(0)
}

func (m *MockRedisService) Get(ctx context.Context, key string, name ...string) (*gvar.Var, error) {
	args := m.Called(ctx, key, name)
	return args.Get(0).(*gvar.Var), args.Error(1)
}

func (m *MockRedisService) GetString(ctx context.Context, key string, name ...string) (string, error) {
	args := m.Called(ctx, key, name)
	return args.String(0), args.Error(1)
}

func (m *MockRedisService) Remove(ctx context.Context, key string, name ...string) error {
	args := m.Called(ctx, key, name)
	return args.Error(0)
}

func (m *MockRedisService) Exists(ctx context.Context, key string, name ...string) (bool, error) {
	args := m.Called(ctx, key, name)
	return args.Bool(0), args.Error(1)
}

func (m *MockRedisService) Incr(ctx context.Context, key string, name ...string) (int64, error) {
	args := m.Called(ctx, key, name)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisService) Decr(ctx context.Context, key string, name ...string) (int64, error) {
	args := m.Called(ctx, key, name)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisService) LPush(ctx context.Context, key string, values ...interface{}) (int64, error) {
	args := m.Called(ctx, key, values)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisService) RPop(ctx context.Context, key string) (*gvar.Var, error) {
	args := m.Called(ctx, key)
	return args.Get(0).(*gvar.Var), args.Error(1)
}

func (m *MockRedisService) HSet(ctx context.Context, key string, field string, value interface{}) (int64, error) {
	args := m.Called(ctx, key, field, value)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisService) HGet(ctx context.Context, key string, field string) (*gvar.Var, error) {
	args := m.Called(ctx, key, field)
	return args.Get(0).(*gvar.Var), args.Error(1)
}

func (m *MockRedisService) HGetAll(ctx context.Context, key string) (*gvar.Var, error) {
	args := m.Called(ctx, key)
	return args.Get(0).(*gvar.Var), args.Error(1)
}

func (m *MockRedisService) ClearCache() {
	m.Called()
}

func (m *MockRedisService) IncrementPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisService) GetPaymentPasswordAttempts(ctx context.Context, userID uint64) (int64, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisService) ResetPaymentPasswordAttempts(ctx context.Context, userID uint64) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

func (m *MockRedisService) LockPaymentPassword(ctx context.Context, userID uint64, duration time.Duration) error {
	args := m.Called(ctx, userID, duration)
	return args.Error(0)
}

func (m *MockRedisService) IsPaymentPasswordLocked(ctx context.Context, userID uint64) (bool, error) {
	args := m.Called(ctx, userID)
	return args.Bool(0), args.Error(1)
}

func (m *MockRedisService) SetUserLanguage(ctx context.Context, telegramID int64, language string, name ...string) error {
	args := m.Called(ctx, telegramID, language, name)
	return args.Error(0)
}

func (m *MockRedisService) GetUserLanguage(ctx context.Context, telegramID int64, name ...string) (string, error) {
	args := m.Called(ctx, telegramID, name)
	return args.String(0), args.Error(1)
}

// TestGenerateLockKey tests the lock key generation
func TestGenerateLockKey(t *testing.T) {
	tests := []struct {
		name            string
		transactionType idempotency.TransactionType
		transactionID   interface{}
		expected        string
	}{
		{
			name:            "User withdrawal with int64 ID",
			transactionType: idempotency.TransactionTypeUserWithdrawal,
			transactionID:   int64(123456),
			expected:        "idempotency:lock:user_withdrawal:123456",
		},
		{
			name:            "Merchant withdrawal with string ID",
			transactionType: idempotency.TransactionTypeMerchantWithdrawal,
			transactionID:   "MW-789",
			expected:        "idempotency:lock:merchant_withdrawal:MW-789",
		},
		{
			name:            "Merchant settlement with uint64 ID",
			transactionType: idempotency.TransactionTypeMerchantSettlement,
			transactionID:   uint64(999888),
			expected:        "idempotency:lock:merchant_settlement:999888",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := idempotency.GenerateLockKey(tt.transactionType, tt.transactionID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestRedisLockerTryLock_Success tests successful lock acquisition
func TestRedisLockerTryLock_Success(t *testing.T) {
	// Note: This test requires actual Redis connection or more complex mocking
	// For now, we'll test the basic structure
	
	mockRedis := new(MockRedisService)
	
	// Create locker with mocked Redis
	locker := idempotency.NewRedisLocker(mockRedis, true, time.Hour)
	
	// Test that locker is created properly
	assert.NotNil(t, locker)
	assert.Equal(t, time.Hour, locker.GetDefaultTTL())
	assert.True(t, locker.IsEnabled())
}

// TestRedisLockerTryLock_Disabled tests behavior when idempotency is disabled
func TestRedisLockerTryLock_Disabled(t *testing.T) {
	ctx := context.Background()
	mockRedis := new(MockRedisService)
	
	// Create locker with idempotency disabled
	locker := idempotency.NewRedisLocker(mockRedis, false, time.Hour)
	
	// When disabled, TryLock should always return true
	acquired, err := locker.TryLock(ctx, "test-key", time.Minute)
	
	assert.True(t, acquired)
	assert.NoError(t, err)
	
	// Redis should not be called when disabled
	mockRedis.AssertNotCalled(t, "Client")
}

// TestConfig tests configuration helper methods
func TestConfig(t *testing.T) {
	t.Run("DefaultConfig", func(t *testing.T) {
		cfg := idempotency.DefaultConfig()
		
		assert.True(t, cfg.Enabled)
		assert.Equal(t, 3600, cfg.LockTTL)
		assert.Equal(t, 5000, cfg.RedisTimeout)
	})
	
	t.Run("GetTTLDuration", func(t *testing.T) {
		cfg := &idempotency.Config{LockTTL: 120}
		assert.Equal(t, 2*time.Minute, cfg.GetTTLDuration())
		
		// Test default when LockTTL is 0
		cfg.LockTTL = 0
		assert.Equal(t, time.Hour, cfg.GetTTLDuration())
	})
	
	t.Run("GetRedisTimeoutDuration", func(t *testing.T) {
		cfg := &idempotency.Config{RedisTimeout: 3000}
		assert.Equal(t, 3*time.Second, cfg.GetRedisTimeoutDuration())
		
		// Test default when RedisTimeout is 0
		cfg.RedisTimeout = 0
		assert.Equal(t, 5*time.Second, cfg.GetRedisTimeoutDuration())
	})
}

// TestMetrics tests the metrics tracking
func TestMetrics(t *testing.T) {
	// Reset metrics before testing
	idempotency.GlobalMetrics.Reset()
	
	// Test incrementing metrics
	idempotency.GlobalMetrics.IncrementLockAcquired()
	idempotency.GlobalMetrics.IncrementLockAcquired()
	idempotency.GlobalMetrics.IncrementLockRejected()
	idempotency.GlobalMetrics.IncrementRedisError()
	
	stats := idempotency.GlobalMetrics.GetStats()
	
	assert.Equal(t, uint64(2), stats.LockAcquiredTotal)
	assert.Equal(t, uint64(1), stats.LockRejectedTotal)
	assert.Equal(t, uint64(1), stats.RedisErrorTotal)
	assert.Equal(t, 2, stats.ActiveLocksCount) // Should be 2 from IncrementLockAcquired
	
	// Test effectiveness rate calculation
	rate := stats.GetEffectiveRate()
	expectedRate := (1.0 / 3.0) * 100 // 1 rejected out of 3 total
	assert.InDelta(t, expectedRate, rate, 0.01)
	
	// Test decrementing active locks
	idempotency.GlobalMetrics.DecrementActiveLocks()
	stats = idempotency.GlobalMetrics.GetStats()
	assert.Equal(t, 1, stats.ActiveLocksCount)
	
	// Test reset
	idempotency.GlobalMetrics.Reset()
	stats = idempotency.GlobalMetrics.GetStats()
	assert.Equal(t, uint64(0), stats.LockAcquiredTotal)
	assert.Equal(t, uint64(0), stats.LockRejectedTotal)
	assert.Equal(t, uint64(0), stats.RedisErrorTotal)
	assert.Equal(t, 0, stats.ActiveLocksCount)
}

// TestMetricsHandler tests the metrics handler
func TestMetricsHandler(t *testing.T) {
	// Reset and set up some metrics
	idempotency.GlobalMetrics.Reset()
	idempotency.GlobalMetrics.IncrementLockAcquired()
	idempotency.GlobalMetrics.IncrementLockRejected()
	
	handler := idempotency.NewMetricsHandler()
	
	t.Run("GetMetricsJSON", func(t *testing.T) {
		jsonStr, err := handler.GetMetricsJSON()
		assert.NoError(t, err)
		assert.Contains(t, jsonStr, "idempotency_lock_acquired_total")
		assert.Contains(t, jsonStr, "1")
	})
	
	t.Run("GetMetricsPrometheus", func(t *testing.T) {
		promStr := handler.GetMetricsPrometheus()
		assert.Contains(t, promStr, "# HELP idempotency_lock_acquired_total")
		assert.Contains(t, promStr, "idempotency_lock_acquired_total 1")
		assert.Contains(t, promStr, "idempotency_lock_rejected_total 1")
	})
}
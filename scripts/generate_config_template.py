#!/usr/bin/env python3
"""
配置模板生成器
读取 manifest/config/config.yaml 文件，生成模板文件和变量提取文件

功能特性：
1. 自动提取配置值作为环境变量
2. 支持跳过指定的 key，保持原值不变（如证书内容）
3. 支持自定义变量值覆盖
4. 生成 YAML 模板文件和 JSON 变量文件

使用方法：
1. 修改 skip_keys 集合来指定需要跳过的配置项
2. 修改 custom_variable_values 字典来覆盖特定变量的值
3. 运行脚本生成模板文件
"""

import yaml
import json
import re
import os
from pathlib import Path


def generate_var_name(path, key=""):
    """生成符合规范的变量名"""
    # 转换为大写，用下划线分隔
    if key:
        full_path = f"{path}.{key}" if path else key
    else:
        full_path = path
    
    # 将路径转换为大写变量名格式
    var_name = full_path.replace('.', '_').replace('[', '_').replace(']', '').upper()
    # 添加前缀
    return f"TASK_WITHDRAW_{var_name}"


def extract_all_config_values(data, path="", variables=None, skip_keys=None):
    """递归提取所有配置值作为变量"""
    if variables is None:
        variables = {}
    if skip_keys is None:
        skip_keys = set()
    
    if isinstance(data, dict):
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key
            # 检查当前 key 是否在跳过列表中
            if key in skip_keys:
                print(f"跳过 key: {key} (路径: {new_path})")
                continue
            extract_all_config_values(value, new_path, variables, skip_keys)
    elif isinstance(data, list):
        for i, item in enumerate(data):
            new_path = f"{path}.{i}" if path else str(i)
            extract_all_config_values(item, new_path, variables, skip_keys)
    else:
        # 对于基本类型值，生成变量
        if data is not None and data != "":
            var_name = generate_var_name(path)
            variables[var_name] = data
    
    return variables


def replace_with_template_vars(data, path="", skip_keys=None):
    """将配置值替换为模板变量"""
    if skip_keys is None:
        skip_keys = set()
        
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            new_path = f"{path}.{key}" if path else key
            # 检查当前 key 是否在跳过列表中
            if key in skip_keys:
                # 保持原值不变
                result[key] = value
            else:
                result[key] = replace_with_template_vars(value, new_path, skip_keys)
        return result
    elif isinstance(data, list):
        result = []
        for i, item in enumerate(data):
            new_path = f"{path}.{i}" if path else str(i)
            result.append(replace_with_template_vars(item, new_path, skip_keys))
        return result
    else:
        # 对于基本类型值，替换为变量
        if data is not None and data != "":
            var_name = generate_var_name(path)
            # 根据原始值的类型决定是否加引号
            if isinstance(data, str):
                return f'"${{{var_name}}}"'
            else:
                # 数字、布尔值等不加引号
                return f"${{{var_name}}}"
        return data


def main():
    # 跳过的 key 列表 - 这些 key 将保持原样不替换为变量
    # 支持以下几种使用场景：
    # 1. 证书内容 - 通常很长且不需要作为环境变量
    # 2. 复杂的多行文本内容
    # 3. 固定不变的配置内容
    skip_keys = {
        "certificate",  # 跳过证书内容
        # 可以添加更多需要跳过的 key，例如：
        # "private_key",      # 私钥内容
        # "public_key",       # 公钥内容
        # "license_text",     # 许可证文本
        # "banner_text",      # 横幅文本
        # "help_text",        # 帮助文本
    }
    
    # 用户自定义变量值覆盖配置
    custom_variable_values = {
        "TASK_WITHDRAW_GRPC_CLIENT_USER-SERVICE_ENDPOINTS_0": "grpc-server:50051",
        "TASK_WITHDRAW_REDIS_DEFAULT_ADDRESS": "valkey:6379",
        "TASK_WITHDRAW_CONSUL_ADDRESS": "consul:8500",

    }
    
    # 设置文件路径
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    config_path = project_root / "manifest" / "config" / "config.yaml"
    template_path = project_root / "manifest" / "config" / "config.yaml.template"
    variables_path = project_root / "config_variables.json"
    
    # 检查源文件是否存在
    if not config_path.exists():
        print(f"错误: 配置文件不存在: {config_path}")
        return 1
    
    try:
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        print(f"成功读取配置文件: {config_path}")
        
        # 提取所有配置值作为变量
        variables = extract_all_config_values(config_data, skip_keys=skip_keys)
        
        # 应用用户自定义的变量值覆盖
        for var_name, custom_value in custom_variable_values.items():
            if var_name in variables:
                print(f"覆盖变量 {var_name}: {variables[var_name]} -> {custom_value}")
                variables[var_name] = custom_value
            else:
                print(f"添加自定义变量 {var_name}: {custom_value}")
                variables[var_name] = custom_value
        
        # 生成模板数据
        template_data = replace_with_template_vars(config_data, skip_keys=skip_keys)
        
        # 写入模板文件
        with open(template_path, 'w', encoding='utf-8') as f:
            yaml_content = yaml.dump(template_data, default_flow_style=False, allow_unicode=True, sort_keys=False)
            # 修复双引号转义问题：将 '"${VAR}"' 替换为 "${VAR}"
            yaml_content = re.sub(r"'\"(\$\{[^}]+\})\"'", r'"\1"', yaml_content)
            
            # 修复跳过的 key 的格式问题
            # 读取原始配置文件内容来获取跳过的 key 的原始格式
            with open(config_path, 'r', encoding='utf-8') as orig_f:
                original_content = orig_f.read()
            
            # 为每个跳过的 key 恢复原始格式
            for skip_key in skip_keys:
                # 在原始文件中查找这个 key 的完整格式（包括后面的换行）
                pattern = rf'^({skip_key}:\s*[\|>].*?)(?=^\w|\Z)'
                match = re.search(pattern, original_content, re.MULTILINE | re.DOTALL)
                if match:
                    original_key_content = match.group(1)
                    # 确保内容以换行结尾，为下一个配置项留出空间
                    if not original_key_content.endswith('\n'):
                        original_key_content += '\n'
                    
                    # 在生成的模板中查找并替换这个 key（更精确的匹配）
                    template_pattern = rf'^{skip_key}:.*?(?=^[a-zA-Z_]|\Z)'
                    if re.search(template_pattern, yaml_content, re.MULTILINE | re.DOTALL):
                        yaml_content = re.sub(template_pattern, original_key_content, yaml_content, flags=re.MULTILINE | re.DOTALL)
            
            f.write(yaml_content)
        
        print(f"成功生成模板文件: {template_path}")
        
        # 写入变量文件
        with open(variables_path, 'w', encoding='utf-8') as f:
            json.dump(variables, f, indent=2, ensure_ascii=False)
        
        print(f"成功生成变量文件: {variables_path}")
        print(f"提取了 {len(variables)} 个配置变量")
        
        return 0
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
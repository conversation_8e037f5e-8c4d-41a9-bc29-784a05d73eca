package enhanced_consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	taskv1 "task-withdraw/api"
	"task-withdraw/internal/logic/task/idempotency"
	"task-withdraw/internal/logic/task/merchant_handler"
	wp "task-withdraw/internal/logic/task/withdrawal_processor"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/eth"
	"task-withdraw/internal/logic/task/withdrawal_processor/sender/tron"
	"task-withdraw/internal/logic/transaction"
	"task-withdraw/internal/model"
	"task-withdraw/internal/service"

	"github.com/gogf/gf/v2/os/glog"
)

// ProcessEnhancedMessage handles the unified transaction message processing
func ProcessEnhancedMessage(
	ctx context.Context,
	logPrefix string,
	message string,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	// Add panic recovery for safety
	defer func() {
		if r := recover(); r != nil {
			err := fmt.Errorf("panic recovered during enhanced message processing: %v", r)
			glog.Criticalf(ctx, "%s %v", logPrefix, err)
			MoveToDlq(ctx, logPrefix, message, dlqName, err)
		}
	}()

	// Parse the unified transaction request
	var transactionReq model.TransactionRequest
	if err := json.Unmarshal([]byte(message), &transactionReq); err != nil {
		glog.Errorf(ctx, "%s Failed to unmarshal transaction request: %v. Message: %s", logPrefix, err, message)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
		return
	}

	// Validate the transaction request
	if err := transactionReq.Validate(); err != nil {
		glog.Errorf(ctx, "%s Invalid transaction request: %v", logPrefix, err)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
		return
	}

	// Create enhanced log prefix with transaction info
	enhancedLogPrefix := fmt.Sprintf("%s[%s][ID:%d][OrderNo:%s]",
		logPrefix, transactionReq.Type, transactionReq.GetTransactionID(), transactionReq.GetOrderNo())

	glog.Infof(ctx, "%s Start processing transaction. Retries so far: %d",
		enhancedLogPrefix, transactionReq.GetRetries())

	// Idempotency check to prevent duplicate processing
	if processorCfg.Idempotency.Enabled {
		// Create locker instance
		locker := idempotency.NewRedisLocker(
			service.Redis(),
			processorCfg.Idempotency.Enabled,
			time.Duration(processorCfg.Idempotency.LockTTL)*time.Second,
		)

		// Map transaction type to idempotency transaction type
		var idempotencyTxType idempotency.TransactionType
		switch transactionReq.Type {
		case model.TransactionTypeUserWithdrawal:
			idempotencyTxType = idempotency.TransactionTypeUserWithdrawal
		case model.TransactionTypeMerchantWithdrawal:
			idempotencyTxType = idempotency.TransactionTypeMerchantWithdrawal
		case model.TransactionTypeMerchantSettlement:
			idempotencyTxType = idempotency.TransactionTypeMerchantSettlement
		default:
			// Unknown type - skip idempotency check
			glog.Warningf(ctx, "%s Unknown transaction type for idempotency check: %s", enhancedLogPrefix, transactionReq.Type)
			goto skipIdempotency
		}

		// Generate lock key for this transaction
		lockKey := idempotency.GenerateLockKey(idempotencyTxType, transactionReq.GetTransactionID())

		// Try to acquire the lock
		lockAcquired, lockErr := locker.TryLock(ctx, lockKey, time.Duration(processorCfg.Idempotency.LockTTL)*time.Second)

		if lockErr != nil {
			// Redis error - log and decide whether to proceed or fail
			glog.Errorf(ctx, "%s Failed to check idempotency lock: %v. Processing anyway (degraded mode).", enhancedLogPrefix, lockErr)
			// In degraded mode, we allow processing to continue
		} else if !lockAcquired {
			// Lock already exists - another worker is processing this transaction
			glog.Infof(ctx, "%s Transaction already being processed by another worker (idempotency check). Discarding message.", enhancedLogPrefix)
			return // Don't process, don't send to DLQ
		} else {
			glog.Debugf(ctx, "%s Acquired idempotency lock. Continuing with processing.", enhancedLogPrefix)
		}
	}
skipIdempotency:

	// Route to appropriate processor based on transaction type
	switch transactionReq.Type {
	case model.TransactionTypeUserWithdrawal:
		// Process user withdrawal using the transaction processor
		processUserWithdrawalSimple(ctx, enhancedLogPrefix, transactionReq.UserWithdrawal, dlqName, processorCfg, limiter, ethSender, tronSender)

	case model.TransactionTypeMerchantWithdrawal:
		processMerchantWithdrawalSimple(ctx, enhancedLogPrefix, transactionReq.MerchantWithdrawal, dlqName, processorCfg, limiter, ethSender, tronSender)

	case model.TransactionTypeMerchantSettlement:
		processMerchantSettlementSimple(ctx, enhancedLogPrefix, transactionReq.MerchantSettlement, dlqName, processorCfg, limiter, ethSender, tronSender)

	default:
		err := fmt.Errorf("unknown transaction type: %s", transactionReq.Type)
		glog.Errorf(ctx, "%s %v", enhancedLogPrefix, err)
		MoveToDlq(ctx, logPrefix, message, dlqName, err)
	}
}

// processUserWithdrawalSimple handles user withdrawal processing with REAL transactions
func processUserWithdrawalSimple(
	ctx context.Context,
	logPrefix string,
	withdrawal *taskv1.Withdrawal,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing user withdrawal ID: %d (REAL TRANSACTION)", logPrefix, withdrawal.UserWithdrawsId)

	// Create transaction processor
	txProcessor := transaction.NewTransactionProcessor(processorCfg, limiter, ethSender, tronSender)

	// Process the withdrawal using the transaction package
	txHash, finalState, errorMessage := txProcessor.ProcessWithdrawal(ctx, withdrawal)

	// Log the result
	if finalState == 4 {
		glog.Infof(ctx, "%s User withdrawal %d processed successfully. TxHash: %s",
			logPrefix, withdrawal.UserWithdrawsId, txHash)
	} else if finalState == 2 {
		glog.Warningf(ctx, "%s User withdrawal %d pending retry: %s",
			logPrefix, withdrawal.UserWithdrawsId, errorMessage)
	} else {
		glog.Errorf(ctx, "%s User withdrawal %d failed: %s",
			logPrefix, withdrawal.UserWithdrawsId, errorMessage)
	}

	// Determine auto_withdrawal_progress based on final state
	var autoWithdrawalProgress int32
	if finalState == 4 {
		// Success - set to 2 (completed)
		autoWithdrawalProgress = 2
	} else if finalState == 2 {
		// Pending retry - keep at 1 (processing)
		autoWithdrawalProgress = 1
	} else {
		// Failed - keep at 1 (processing failed but don't reset to 0)
		autoWithdrawalProgress = 1
	}

	// Push status update for user withdrawals
	statusUpdateMessage := map[string]interface{}{
		"withdrawal_id":            withdrawal.UserWithdrawsId,
		"target_state":             finalState,
		"tx_hash":                  txHash,
		"error_message":            errorMessage,
		"retries":                  withdrawal.Retries,
		"auto_withdrawal_progress": autoWithdrawalProgress,
	}

	statusUpdateJSON, err := json.Marshal(statusUpdateMessage)
	if err != nil {
		glog.Errorf(ctx, "%s Failed to marshal status update: %v", logPrefix, err)
		MoveToDlq(ctx, logPrefix, fmt.Sprintf("user_withdrawal:%d", withdrawal.UserWithdrawsId), dlqName, err)
		return
	}

	// Push to the user withdrawal status update queue
	redisClient := service.Redis().Client()
	_, err = redisClient.LPush(ctx, "queue:withdrawal_status_update", string(statusUpdateJSON))
	if err != nil {
		glog.Errorf(ctx, "%s Failed to push user withdrawal status update: %v", logPrefix, err)
		MoveToDlq(ctx, logPrefix, fmt.Sprintf("user_withdrawal:%d", withdrawal.UserWithdrawsId), dlqName, err)
	} else {
		glog.Infof(ctx, "%s Successfully pushed user withdrawal status update for ID %d",
			logPrefix, withdrawal.UserWithdrawsId)
	}
}

// processMerchantWithdrawalSimple handles merchant withdrawal processing with REAL transactions
func processMerchantWithdrawalSimple(
	ctx context.Context,
	logPrefix string,
	withdrawal *taskv1.MerchantWithdraw,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing merchant withdrawal ID: %d (REAL TRANSACTION)", logPrefix, withdrawal.WithdrawsId)

	// Convert merchant withdrawal to user withdrawal format
	userWithdrawal := convertMerchantWithdrawalToUser(withdrawal)

	// Create transaction processor
	txProcessor := transaction.NewTransactionProcessor(processorCfg, limiter, ethSender, tronSender)

	// Process the withdrawal using the transaction package
	txHash, finalState, errorMessage := txProcessor.ProcessWithdrawal(ctx, userWithdrawal)

	// Log the result
	if finalState == 4 {
		glog.Infof(ctx, "%s Merchant withdrawal %d processed successfully. TxHash: %s",
			logPrefix, withdrawal.WithdrawsId, txHash)
	} else if finalState == 2 {
		glog.Warningf(ctx, "%s Merchant withdrawal %d pending retry: %s",
			logPrefix, withdrawal.WithdrawsId, errorMessage)
	} else {
		glog.Errorf(ctx, "%s Merchant withdrawal %d failed: %s",
			logPrefix, withdrawal.WithdrawsId, errorMessage)
	}

	// Push merchant status update with real transaction result
	statusErr := merchant_handler.PushMerchantStatusUpdate(
		ctx,
		"merchant_withdrawal",
		withdrawal.WithdrawsId,
		finalState,   // Real state: 4=success, 2=retry, 5=failed
		txHash,       // Real transaction hash
		errorMessage, // Real error message if any
		withdrawal.Retries,
	)

	if statusErr != nil {
		glog.Errorf(ctx, "%s Failed to push merchant status update: %v", logPrefix, statusErr)
		// Move to DLQ if we can't push the status update
		MoveToDlq(ctx, logPrefix, fmt.Sprintf("merchant_withdrawal:%d", withdrawal.WithdrawsId), dlqName, statusErr)
	}
}

// processMerchantSettlementSimple handles merchant settlement processing with REAL transactions
func processMerchantSettlementSimple(
	ctx context.Context,
	logPrefix string,
	settlement *taskv1.MerchantSettlement,
	dlqName string,
	processorCfg *wp.WithdrawalConfig,
	limiter *wp.MemoryRateLimiter,
	ethSender *eth.EthSender,
	tronSender *tron.TronSender,
) {
	glog.Infof(ctx, "%s Processing merchant settlement ID: %d (REAL TRANSACTION)", logPrefix, settlement.SettlementsId)

	// Convert merchant settlement to user withdrawal format
	userWithdrawal := convertMerchantSettlementToUser(settlement)

	// Create transaction processor
	txProcessor := transaction.NewTransactionProcessor(processorCfg, limiter, ethSender, tronSender)

	// Process the settlement using the transaction package
	txHash, finalState, errorMessage := txProcessor.ProcessWithdrawal(ctx, userWithdrawal)

	// Log the result
	if finalState == 4 {
		glog.Infof(ctx, "%s Merchant settlement %d processed successfully. TxHash: %s",
			logPrefix, settlement.SettlementsId, txHash)
	} else if finalState == 2 {
		glog.Warningf(ctx, "%s Merchant settlement %d pending retry: %s",
			logPrefix, settlement.SettlementsId, errorMessage)
	} else {
		glog.Errorf(ctx, "%s Merchant settlement %d failed: %s",
			logPrefix, settlement.SettlementsId, errorMessage)
	}

	// Push merchant status update with real transaction result
	statusErr := merchant_handler.PushMerchantStatusUpdate(
		ctx,
		"merchant_settlement",
		settlement.SettlementsId,
		finalState,   // Real state: 4=success, 2=retry, 5=failed
		txHash,       // Real transaction hash
		errorMessage, // Real error message if any
		settlement.Retries,
	)

	if statusErr != nil {
		glog.Errorf(ctx, "%s Failed to push merchant settlement status update: %v", logPrefix, statusErr)
		// Move to DLQ if we can't push the status update
		MoveToDlq(ctx, logPrefix, fmt.Sprintf("merchant_settlement:%d", settlement.SettlementsId), dlqName, statusErr)
	}
}

// convertMerchantWithdrawalToUser converts a merchant withdrawal to user withdrawal format
func convertMerchantWithdrawalToUser(merchant *taskv1.MerchantWithdraw) *taskv1.Withdrawal {
	return &taskv1.Withdrawal{
		UserWithdrawsId:  merchant.WithdrawsId,
		UserId:           merchant.MerchantId,
		TokenId:          merchant.TokenId,
		WalletId:         merchant.WalletId,
		Name:             merchant.Name,
		Chan:             merchant.Chan,
		OrderNo:          merchant.OrderNo,
		Address:          merchant.Address,
		RecipientName:    merchant.RecipientName,
		RecipientAccount: merchant.RecipientAccount,
		Amount:           merchant.Amount,
		HandlingFee:      merchant.HandlingFee,
		ActualAmount:     merchant.ActualAmount,
		// Map merchant state to user withdrawal statuses
		AuditStatus:            mapMerchantStateToAuditStatus(merchant.State),
		AutoWithdrawalProgress: 0, // Merchant withdrawals don't use this field
		ProcessingStatus:       mapMerchantStateToProcessingStatus(merchant.State),
		RefuseReasonZh:         merchant.RefuseReasonZh,
		RefuseReasonEn:         merchant.RefuseReasonEn,
		TxHash:                 merchant.TxHash,
		ErrorMessage:           merchant.ErrorMessage,
		UserRemark:             merchant.UserRemark,
		AdminRemark:            merchant.AdminRemark,
		CreatedAt:              merchant.CreatedAt,
		CheckedAt:              merchant.CheckedAt,
		ProcessingAt:           merchant.ProcessingAt,
		CompletedAt:            merchant.CompletedAt,
		UpdatedAt:              merchant.UpdatedAt,
		Retries:                merchant.Retries,
		NergyState:             merchant.NergyState,
	}
}

// convertMerchantSettlementToUser converts a merchant settlement to user withdrawal format
func convertMerchantSettlementToUser(settlement *taskv1.MerchantSettlement) *taskv1.Withdrawal {
	return &taskv1.Withdrawal{
		UserWithdrawsId:  settlement.SettlementsId,
		UserId:           settlement.MerchantId,
		TokenId:          settlement.TokenId,
		WalletId:         settlement.WalletId,
		Name:             settlement.Name,
		Chan:             settlement.Chan,
		OrderNo:          settlement.OrderNo,
		Address:          settlement.Address,
		RecipientName:    settlement.RecipientName,
		RecipientAccount: settlement.RecipientAccount,
		Amount:           settlement.Amount,
		HandlingFee:      settlement.HandlingFee,
		ActualAmount:     settlement.ActualAmount,
		// Map settlement state to user withdrawal statuses
		AuditStatus:            mapMerchantStateToAuditStatus(settlement.State),
		AutoWithdrawalProgress: 0, // Settlements don't use this field
		ProcessingStatus:       mapMerchantStateToProcessingStatus(settlement.State),
		RefuseReasonZh:         settlement.RefuseReasonZh,
		RefuseReasonEn:         settlement.RefuseReasonEn,
		TxHash:                 settlement.TxHash,
		ErrorMessage:           settlement.ErrorMessage,
		UserRemark:             settlement.UserRemark,
		AdminRemark:            settlement.AdminRemark,
		CreatedAt:              settlement.CreatedAt,
		CheckedAt:              settlement.CheckedAt,
		ProcessingAt:           settlement.ProcessingAt,
		CompletedAt:            settlement.CompletedAt,
		UpdatedAt:              settlement.UpdatedAt,
		Retries:                settlement.Retries,
		NergyState:             settlement.NergyState,
	}
}

// mapMerchantStateToAuditStatus maps merchant state to user withdrawal audit status
func mapMerchantStateToAuditStatus(state int32) int32 {
	switch state {
	case 1: // 待审核
		return 2 // 待审核
	case 2: // 处理中
		return 3 // 审核通过
	case 3: // 已拒绝
		return 4 // 审核拒绝
	case 4: // 已完成
		return 3 // 审核通过
	case 5: // 失败
		return 3 // 审核通过 (failed during processing, not audit)
	default:
		return 2 // 默认待审核
	}
}

// mapMerchantStateToProcessingStatus maps merchant state to user withdrawal processing status
func mapMerchantStateToProcessingStatus(state int32) int32 {
	switch state {
	case 1: // 待审核
		return 0 // 未开始处理
	case 2: // 处理中
		return 1 // 自动放币处理中
	case 3: // 已拒绝
		return 5 // 失败
	case 4: // 已完成
		return 4 // 成功
	case 5: // 失败
		return 5 // 失败
	default:
		return 0 // 默认未开始
	}
}

// MoveToDlq moves a message to the dead letter queue
func MoveToDlq(ctx context.Context, logPrefix, message, dlqName string, err error) {
	glog.Errorf(ctx, "%s Moving message to DLQ '%s' due to error: %v. Message: %s", logPrefix, dlqName, err, message)
	// Implementation would move the message to DLQ
}

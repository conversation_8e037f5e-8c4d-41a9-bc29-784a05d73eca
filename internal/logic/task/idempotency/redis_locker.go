package idempotency

import (
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/os/glog"
	"task-withdraw/internal/service"
)

// RedisLocker implements Locker interface using Redis
type RedisLocker struct {
	redis service.IRedis
	// Configuration
	enabled bool
	defaultTTL time.Duration
}

// NewRedisLocker creates a new RedisLocker instance
func NewRedisLocker(redis service.IRedis, enabled bool, defaultTTL time.Duration) *RedisLocker {
	if defaultTTL <= 0 {
		defaultTTL = time.Hour // Default to 1 hour if not specified
	}
	return &RedisLocker{
		redis:      redis,
		enabled:    enabled,
		defaultTTL: defaultTTL,
	}
}

// TryLock attempts to acquire a distributed lock using Redis SET NX EX
func (r *RedisLocker) TryLock(ctx context.Context, key string, ttl time.Duration) (bool, error) {
	// Check if idempotency is enabled
	if !r.enabled {
		glog.Debugf(ctx, "Idempotency check disabled, skipping lock for key: %s", key)
		return true, nil // Allow processing when disabled
	}

	// Use default TTL if not specified
	if ttl <= 0 {
		ttl = r.defaultTTL
	}

	// Get Redis client
	client := r.redis.Client()
	if client == nil {
		return false, fmt.Errorf("redis client is nil")
	}

	// Use SET NX EX atomic command
	// NX: Only set the key if it does not already exist
	// EX: Set the specified expire time, in seconds
	ttlSeconds := int64(ttl.Seconds())
	
	// Execute SET key value NX EX ttl
	// The value can be anything, we use "1" for simplicity
	result, err := client.Do(ctx, "SET", key, "1", "NX", "EX", ttlSeconds)
	if err != nil {
		glog.Errorf(ctx, "Failed to execute SET NX EX for key %s: %v", key, err)
		GlobalMetrics.IncrementRedisError()
		return false, fmt.Errorf("failed to acquire lock: %w", err)
	}

	// Check if the lock was acquired
	// SET NX returns OK if the key was set, nil if it already existed
	if result != nil && result.String() == "OK" {
		glog.Debugf(ctx, "Successfully acquired lock for key: %s (TTL: %v)", key, ttl)
		GlobalMetrics.IncrementLockAcquired()
		return true, nil
	}

	// Lock already exists - another worker is processing this transaction
	glog.Infof(ctx, "Lock already exists for key: %s - transaction is being processed by another worker", key)
	GlobalMetrics.IncrementLockRejected()
	return false, nil
}

// IsEnabled returns whether idempotency checking is enabled
func (r *RedisLocker) IsEnabled() bool {
	return r.enabled
}

// SetEnabled enables or disables idempotency checking
func (r *RedisLocker) SetEnabled(enabled bool) {
	r.enabled = enabled
}

// GetDefaultTTL returns the default TTL for locks
func (r *RedisLocker) GetDefaultTTL() time.Duration {
	return r.defaultTTL
}

// SetDefaultTTL sets the default TTL for locks
func (r *RedisLocker) SetDefaultTTL(ttl time.Duration) {
	if ttl > 0 {
		r.defaultTTL = ttl
	}
}
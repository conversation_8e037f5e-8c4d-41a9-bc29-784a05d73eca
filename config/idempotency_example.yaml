# Idempotency Configuration Example
# This configuration prevents duplicate processing of withdrawal orders
# through Redis-based distributed locks

withdrawalProcessor:
  # Existing withdrawal processor configuration
  enabled: true
  spec: "*/10 * * * * *"
  batchSize: 10
  
  # Idempotency configuration
  idempotency:
    # Enable/disable idempotency checking
    # When disabled, duplicate processing prevention is turned off
    enabled: true
    
    # Lock TTL in seconds
    # This should be longer than the maximum time required to process a single order
    # Default: 3600 (1 hour)
    # Recommended: 1800-7200 (30 minutes to 2 hours)
    lockTtl: 3600
    
    # Redis operation timeout in milliseconds
    # If Redis doesn't respond within this time, the operation fails
    # Default: 5000 (5 seconds)
    # Recommended: 2000-10000 (2-10 seconds)
    redisTimeout: 5000

# Example configurations for different environments:

# Development environment (shorter TTL for testing)
development:
  withdrawalProcessor:
    idempotency:
      enabled: true
      lockTtl: 300  # 5 minutes
      redisTimeout: 3000

# Staging environment (moderate settings)
staging:
  withdrawalProcessor:
    idempotency:
      enabled: true
      lockTtl: 1800  # 30 minutes
      redisTimeout: 5000

# Production environment (conservative settings)
production:
  withdrawalProcessor:
    idempotency:
      enabled: true
      lockTtl: 7200  # 2 hours
      redisTimeout: 10000  # 10 seconds for better reliability

# High-load environment (optimized for performance)
high_load:
  withdrawalProcessor:
    idempotency:
      enabled: true
      lockTtl: 3600  # 1 hour
      redisTimeout: 2000  # 2 seconds for faster failure detection
package idempotency

import (
	"time"
)

// Config holds configuration for idempotency control
type Config struct {
	// Enabled determines if idempotency checking is active
	Enabled bool `json:"enabled"`
	
	// LockTTL is the time-to-live for locks in seconds
	// Default: 3600 (1 hour)
	LockTTL int `json:"lockTtl"`
	
	// RedisTimeout is the timeout for Redis operations in milliseconds
	// Default: 5000 (5 seconds)
	RedisTimeout int `json:"redisTimeout"`
}

// GetTTLDuration returns the lock TTL as a time.Duration
func (c *Config) GetTTLDuration() time.Duration {
	if c.LockTTL <= 0 {
		return time.Hour // Default to 1 hour
	}
	return time.Duration(c.LockTTL) * time.Second
}

// GetRedisTimeoutDuration returns the Redis timeout as a time.Duration
func (c *Config) GetRedisTimeoutDuration() time.Duration {
	if c.RedisTimeout <= 0 {
		return 5 * time.Second // Default to 5 seconds
	}
	return time.Duration(c.RedisTimeout) * time.Millisecond
}

// DefaultConfig returns the default idempotency configuration
func DefaultConfig() *Config {
	return &Config{
		Enabled:      true,
		LockTTL:      3600, // 1 hour in seconds
		RedisTimeout: 5000, // 5 seconds in milliseconds
	}
}
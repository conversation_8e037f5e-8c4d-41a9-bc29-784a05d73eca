package idempotency

import (
	"context"
	"fmt"
	"strconv"
	"time"
)

// Locker provides distributed lock capabilities for idempotency control.
// It is used to prevent concurrent processing of the same transaction.
type Locker interface {
	// TryLock attempts to acquire a distributed lock for the given key.
	// Returns true if the lock was successfully acquired, false if it already exists.
	// Returns an error only if there's a problem communicating with the backing store.
	TryLock(ctx context.Context, key string, ttl time.Duration) (bool, error)
}

// TransactionType represents the type of transaction for lock key generation
type TransactionType string

const (
	TransactionTypeUserWithdrawal     TransactionType = "user_withdrawal"
	TransactionTypeMerchantWithdrawal TransactionType = "merchant_withdrawal"
	TransactionTypeMerchantSettlement TransactionType = "merchant_settlement"
)

// GenerateLockKey generates a lock key for idempotency control
// Format: idempotency:lock:{transaction_type}:{transaction_id}
func GenerateLockKey(transactionType TransactionType, transactionID interface{}) string {
	return "idempotency:lock:" + string(transactionType) + ":" + formatID(transactionID)
}

// formatID converts various ID types to string
func formatID(id interface{}) string {
	switch v := id.(type) {
	case string:
		return v
	case int:
		return strconv.Itoa(v)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int64:
		return strconv.FormatInt(v, 10)
	case uint:
		return strconv.FormatUint(uint64(v), 10)
	case uint32:
		return strconv.FormatUint(uint64(v), 10)
	case uint64:
		return strconv.FormatUint(v, 10)
	default:
		return fmt.Sprintf("%v", id)
	}
}
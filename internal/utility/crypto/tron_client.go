package crypto

import (
	"context"
	"crypto/ecdsa"
	"crypto/tls"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"time"

	"google.golang.org/grpc/credentials"

	"task-withdraw/internal/utility/utils/bip39"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/fbsobreira/gotron-sdk/pkg/client"
	"github.com/fbsobreira/gotron-sdk/pkg/common"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/api"
	"github.com/fbsobreira/gotron-sdk/pkg/proto/core"
	"github.com/shopspring/decimal"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	tronwallet "task-withdraw/internal/utility/tron-wallet"
)

// TRONClient implements BlockchainClient for TRON
type TRONClient struct {
	client *client.GrpcClient
	config *ClientConfig
}

type auth struct {
	token string
}

func (a *auth) GetRequestMetadata(ctx context.Context, uri ...string) (map[string]string, error) {
	return map[string]string{
		"x-token": a.token,
	}, nil
}

func (a *auth) RequireTransportSecurity() bool {
	return false
}

// NewTRONClient creates a new TRON client
func NewTRONClient(config *ClientConfig) (*TRONClient, error) {
	if config == nil || config.RPCUrl == "" {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"TRON RPC URL is required",
			ChainTRON,
		)
	}

	// Set up dial options based on whether the connection needs TLS
	var dialOptions []grpc.DialOption

	// Determine if TLS should be used
	useTLS := config.UseTLS
	if !useTLS {
		// Auto-detect TLS requirement based on common endpoints if not explicitly set
		// Check for HTTPS prefix or known secure endpoints
		if strings.HasPrefix(config.RPCUrl, "https://") ||
			strings.Contains(config.RPCUrl, "quiknode") ||
			strings.Contains(config.RPCUrl, "ankr") ||
			strings.Contains(config.RPCUrl, "infura") {
			useTLS = false
		}
		// Special handling for TronGrid
		// Nile testnet (grpc.nile.trongrid.io) uses insecure connection
		// Mainnet (grpc.trongrid.io) uses TLS
		if strings.Contains(config.RPCUrl, "trongrid") && !strings.Contains(config.RPCUrl, "nile") {
			useTLS = false
		}
	}

	// For HTTPS URLs, extract the host:port for gRPC connection
	rpcURL := config.RPCUrl
	if strings.HasPrefix(rpcURL, "https://") {
		// Remove the https:// prefix for gRPC connection
		rpcURL = strings.TrimPrefix(rpcURL, "https://")
		// Remove trailing slash if present
		rpcURL = strings.TrimSuffix(rpcURL, "/")
		// If no port specified, add default gRPC port
		if !strings.Contains(rpcURL, ":") {
			rpcURL = rpcURL + ":50051"
		}
		useTLS = true // Force TLS for HTTPS URLs
	} else if strings.HasPrefix(rpcURL, "http://") {
		// Remove the http:// prefix for gRPC connection
		rpcURL = strings.TrimPrefix(rpcURL, "http://")
		rpcURL = strings.TrimSuffix(rpcURL, "/")
		if !strings.Contains(rpcURL, ":") {
			rpcURL = rpcURL + ":50051"
		}
		useTLS = false // Force insecure for HTTP URLs
	}

	// Create the client with the processed URL
	grpcClient := client.NewGrpcClient(rpcURL)

	// Set API Key if provided (must be after creating client)
	if config.APIKey != "" {
		if err := grpcClient.SetAPIKey(config.APIKey); err != nil {
			return nil, NewBlockchainError(
				ErrCodeNetworkError,
				"failed to set TRON API key",
				ChainTRON,
				map[string]interface{}{"error": err.Error()},
			)
		}
	}

	// Configure transport credentials
	if useTLS {
		// Use TLS for secure endpoints
		dialOptions = []grpc.DialOption{
			grpc.WithTransportCredentials(credentials.NewTLS(&tls.Config{})),
		}
	} else {
		// Use insecure for local or non-TLS endpoints
		dialOptions = []grpc.DialOption{
			grpc.WithTransportCredentials(insecure.NewCredentials()),
		}
	}

	// Add API key authentication if provided
	if config.APIKey != "" {
		dialOptions = append(dialOptions, grpc.WithPerRPCCredentials(&auth{token: config.APIKey}))
	}

	if err := grpcClient.Start(dialOptions...); err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to start TRON client",
			ChainTRON,
			map[string]interface{}{
				"rpc_url":     config.RPCUrl,
				"use_tls":     useTLS,
				"has_api_key": config.APIKey != "",
				"error":       err.Error(),
			},
		)
	}

	return &TRONClient{
		client: grpcClient,
		config: config,
	}, nil
}

// GetChainType returns the chain type
func (c *TRONClient) GetChainType() ChainType {
	return ChainTRON
}

// GetChainID returns the chain ID (TRON doesn't have traditional chain ID)
func (c *TRONClient) GetChainID(ctx context.Context) (*big.Int, error) {
	// TRON doesn't have a traditional chain ID like Ethereum
	// Return a fixed value to satisfy the interface
	return big.NewInt(728126428), nil // TRON Mainnet identifier
}

// GenerateAddress generates an address from private key
func (c *TRONClient) GenerateAddress(privateKey *ecdsa.PrivateKey) (*Address, error) {
	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return nil, NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"failed to cast public key to ECDSA",
			ChainTRON,
		)
	}

	// Use the proper TRON address generation from tron-wallet package
	address := tronwallet.PublicKeyToAddressBase58(publicKeyECDSA)

	return &Address{
		Address:   address,
		ChainType: ChainTRON,
		CreatedAt: time.Now(),
	}, nil
}

// ValidateAddress validates a TRON address
func (c *TRONClient) ValidateAddress(address string) bool {
	// TRON addresses start with 'T' and are 34 characters long
	if len(address) != 34 || !strings.HasPrefix(address, "T") {
		return false
	}

	// Try to decode using TRON's base58 decoder
	_, err := common.DecodeCheck(address)
	return err == nil
}

// ValidatePrivateKey validates a private key
func (c *TRONClient) ValidatePrivateKey(privateKey string) bool {
	privateKey = strings.TrimPrefix(privateKey, "0x")
	if len(privateKey) != 64 {
		return false
	}

	_, err := hex.DecodeString(privateKey)
	return err == nil
}

// GetAddressFromPrivateKey gets address from private key string
func (c *TRONClient) GetAddressFromPrivateKey(privateKeyStr string) (string, error) {
	privateKeyStr = strings.TrimPrefix(privateKeyStr, "0x")
	privateKey, err := crypto.HexToECDSA(privateKeyStr)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"invalid private key format",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"failed to cast public key to ECDSA",
			ChainTRON,
		)
	}

	// Use the proper TRON address generation from tron-wallet package
	address := tronwallet.PublicKeyToAddressBase58(publicKeyECDSA)
	return address, nil
}

// GenerateMnemonic generates a new mnemonic phrase
func (c *TRONClient) GenerateMnemonic() (string, error) {
	entropy, err := bip39.NewEntropy(128) // 128 bits = 12 words
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"failed to generate entropy",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	mnemonic, err := bip39.NewMnemonic(entropy)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"failed to generate mnemonic",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	return mnemonic, nil
}

// ValidateMnemonic validates a mnemonic phrase
func (c *TRONClient) ValidateMnemonic(mnemonic string) bool {
	return bip39.IsMnemonicValid(mnemonic)
}

// GeneratePrivateKey generates a new private key
func (c *TRONClient) GeneratePrivateKey() (string, error) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"failed to generate private key",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	return hex.EncodeToString(crypto.FromECDSA(privateKey)), nil
}

// GetDerivedPath returns the derivation path for given index
func (c *TRONClient) GetDerivedPath(index int) string {
	return fmt.Sprintf("m/44'/195'/0'/0/%d", index)
}

// DeriveKeyFromPath derives a private key from mnemonic and path
func (c *TRONClient) DeriveKeyFromPath(mnemonic string, path string) (*ecdsa.PrivateKey, error) {
	// This would use the BIP32 implementation
	return nil, NewBlockchainError(
		ErrCodeUnsupportedOperation,
		"HD key derivation not implemented yet",
		ChainTRON,
	)
}

// GetNativeBalance gets TRX balance
func (c *TRONClient) GetNativeBalance(ctx context.Context, address string) (*Balance, error) {
	if !c.ValidateAddress(address) {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid TRON address",
			ChainTRON,
			map[string]interface{}{"address": address},
		)
	}

	account, err := c.client.GetAccount(address)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get TRON account",
			ChainTRON,
			map[string]interface{}{"address": address, "error": err.Error()},
		)
	}

	// Convert from SUN to TRX (1 TRX = 10^6 SUN)
	balanceSun := decimal.NewFromInt(account.GetBalance())
	balance := balanceSun.Div(decimal.NewFromInt(1e6))

	return &Balance{
		Address:     address,
		ChainType:   ChainTRON,
		NativeToken: balance,
		TokenSymbol: "TRX",
		UpdatedAt:   time.Now(),
	}, nil
}

// GetTokenBalance gets TRC20 token balance
func (c *TRONClient) GetTokenBalance(ctx context.Context, address string, contractAddress string) (*Balance, error) {
	if !c.ValidateAddress(address) {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid TRON address",
			ChainTRON,
			map[string]interface{}{"address": address},
		)
	}

	if !c.ValidateAddress(contractAddress) {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid contract address",
			ChainTRON,
			map[string]interface{}{"contract_address": contractAddress},
		)
	}

	// Use the existing gRPC client for TRC20 balance lookup
	if c.client == nil {
		return nil, NewBlockchainError(
			ErrCodeConnectionFailed,
			"TRON gRPC client not initialized",
			ChainTRON,
			map[string]interface{}{"address": address, "contract": contractAddress},
		)
	}

	// Get TRC20 token balance
	balance, err := c.client.TRC20ContractBalance(address, contractAddress)
	if err != nil {
		if strings.Contains(err.Error(), "account not found") {
			// Account not found is not an error, just return zero balance
			return &Balance{
				Address:      address,
				ChainType:    ChainTRON,
				TokenBalance: decimal.Zero,
				TokenSymbol:  "USDT",
				UpdatedAt:    time.Now(),
			}, nil
		}
		return nil, NewBlockchainError(
			ErrCodeBalanceFetchFailed,
			"failed to get TRC20 balance",
			ChainTRON,
			map[string]interface{}{"error": err.Error(), "address": address, "contract": contractAddress},
		)
	}

	// Get token decimals
	decimals, err := c.client.TRC20GetDecimals(contractAddress)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeBalanceFetchFailed,
			"failed to get TRC20 decimals",
			ChainTRON,
			map[string]interface{}{"error": err.Error(), "contract": contractAddress},
		)
	}

	// Convert balance from raw units to decimal with proper precision
	balanceDecimal := decimal.NewFromInt(balance.Int64()).Div(decimal.New(1, int32(decimals.Int64())))

	return &Balance{
		Address:      address,
		ChainType:    ChainTRON,
		TokenBalance: balanceDecimal,
		TokenSymbol:  "USDT",
		UpdatedAt:    time.Now(),
	}, nil
}

// GetAllBalances gets both TRX and token balances
func (c *TRONClient) GetAllBalances(ctx context.Context, address string) (*Balance, error) {
	trxBalance, err := c.GetNativeBalance(ctx, address)
	if err != nil {
		return nil, err
	}

	// Get default contract address for token balance
	tokenBalance := decimal.Zero
	if c.config.ContractAddress != "" {
		tokenBal, err := c.GetTokenBalance(ctx, address, c.config.ContractAddress)
		if err == nil {
			tokenBalance = tokenBal.TokenBalance
		}
	}

	return &Balance{
		Address:      address,
		ChainType:    ChainTRON,
		NativeToken:  trxBalance.NativeToken,
		TokenBalance: tokenBalance,
		TokenSymbol:  "USDT",
		UpdatedAt:    time.Now(),
	}, nil
}

// SendTransaction sends a transaction
func (c *TRONClient) SendTransaction(ctx context.Context, req *SendTransactionRequest) (*Transaction, error) {
	// Get sender address from private key
	fromAddress, err := c.GetAddressFromPrivateKey(req.PrivateKey)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"invalid private key",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	var txHash string

	switch req.TokenType {
	case TokenNative:
		txHash, err = c.sendTRXTransaction(ctx, req)
		if err != nil {
			return nil, err
		}

	case TokenTRC20:
		txHash, err = c.sendTRC20Transaction(ctx, req)
		if err != nil {
			return nil, err
		}

	default:
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"unsupported token type for TRON",
			ChainTRON,
		)
	}

	return &Transaction{
		Hash:            txHash,
		ChainType:       ChainTRON,
		TokenType:       req.TokenType,
		FromAddress:     fromAddress,
		ToAddress:       req.ToAddress,
		Amount:          req.Amount,
		ContractAddress: req.ContractAddress,
		Status:          TxStatusPending,
		CreatedAt:       time.Now(),
	}, nil
}

// EstimateFee estimates transaction fee
func (c *TRONClient) EstimateFee(ctx context.Context, req *SendTransactionRequest) (*FeeEstimate, error) {
	var estimatedFee decimal.Decimal

	switch req.TokenType {
	case TokenNative:
		// TODO: Implement TRX fee estimation
		feeSun := int64(1000) // Default 1000 SUN = 0.001 TRX
		// Convert from SUN to TRX
		estimatedFee = decimal.NewFromInt(feeSun).Div(decimal.NewFromInt(1e6))

	case TokenTRC20:
		// TRC20 transfer fee depends on energy consumption
		// Use a reasonable default based on typical usage
		estimatedFee = decimal.NewFromFloat(1.0) // ~1 TRX typical for TRC20

	default:
		return nil, NewBlockchainError(
			ErrCodeUnsupportedOperation,
			"unsupported token type for fee estimation",
			ChainTRON,
		)
	}

	return &FeeEstimate{
		ChainType:    ChainTRON,
		TokenType:    req.TokenType,
		EstimatedFee: estimatedFee,
		EstimatedAt:  time.Now(),
	}, nil
}

// GetTransaction gets transaction by hash
func (c *TRONClient) GetTransaction(ctx context.Context, hash string) (*Transaction, error) {
	// This would implement transaction lookup
	return nil, NewBlockchainError(
		ErrCodeUnsupportedOperation,
		"get transaction not implemented yet",
		ChainTRON,
	)
}

// GetTransactionStatus gets transaction status
func (c *TRONClient) GetTransactionStatus(ctx context.Context, hash string) (TxStatus, error) {
	// This would implement transaction status lookup
	return TxStatusPending, NewBlockchainError(
		ErrCodeUnsupportedOperation,
		"get transaction status not implemented yet",
		ChainTRON,
	)
}

// FormatAmount formats amount for display
func (c *TRONClient) FormatAmount(amount decimal.Decimal, tokenType TokenType) string {
	switch tokenType {
	case TokenNative:
		return amount.String() + " TRX"
	case TokenTRC20:
		return amount.String() + " USDT"
	default:
		return amount.String()
	}
}

// ParseAmount parses amount string to decimal
func (c *TRONClient) ParseAmount(amountStr string, tokenType TokenType) (decimal.Decimal, error) {
	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return decimal.Zero, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid amount format",
			ChainTRON,
			map[string]interface{}{"amount": amountStr, "error": err.Error()},
		)
	}
	return amount, nil
}

// GetFeeAddress gets fee address for private key
func (c *TRONClient) GetFeeAddress(privateKey string) (string, error) {
	return c.GetAddressFromPrivateKey(privateKey)
}

// GetCurrentBlock gets current block number from TRON
func (c *TRONClient) GetCurrentBlock(ctx context.Context) (uint64, error) {
	currentBlock, err := c.client.GetNowBlock()
	if err != nil {
		return 0, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get current block",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	blockNumber := uint64(currentBlock.GetBlockHeader().GetRawData().GetNumber())
	return blockNumber, nil
}

// GetBlockByNum gets block by number
func (c *TRONClient) GetBlockByNum(ctx context.Context, blockNum int64) (*api.BlockExtention, error) {
	block, err := c.client.GetBlockByNum(blockNum)
	if err != nil {
		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get block by number",
			ChainTRON,
			map[string]interface{}{"block": blockNum, "error": err.Error()},
		)
	}
	return block, nil
}

// GetTransactionByID gets transaction by ID
func (c *TRONClient) GetTransactionByID(ctx context.Context, txID string) (*core.Transaction, error) {
	// Validate transaction ID format
	if len(txID) != 64 {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid transaction ID format",
			ChainTRON,
			map[string]interface{}{"txID": txID, "expected_length": 64, "actual_length": len(txID)},
		)
	}

	tx, err := c.client.GetTransactionByID(txID)
	if err != nil {
		// Check if this is a "transaction not found" error
		errStr := err.Error()
		if isTransactionNotFoundError(errStr) {
			return nil, NewBlockchainError(
				ErrCodeTransactionFailed,
				"transaction not found",
				ChainTRON,
				map[string]interface{}{"txID": txID},
			)
		}

		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get transaction by ID",
			ChainTRON,
			map[string]interface{}{"txID": txID, "error": err.Error()},
		)
	}
	return tx, nil
}

// GetTransactionInfoByID gets transaction info by ID
func (c *TRONClient) GetTransactionInfoByID(ctx context.Context, txID string) (*core.TransactionInfo, error) {
	// Validate transaction ID format
	if len(txID) != 64 {
		return nil, NewBlockchainError(
			ErrCodeInvalidAddress,
			"invalid transaction ID format",
			ChainTRON,
			map[string]interface{}{"txID": txID, "expected_length": 64, "actual_length": len(txID)},
		)
	}

	txInfo, err := c.client.GetTransactionInfoByID(txID)
	if err != nil {
		// Check if this is a "transaction not found" error
		errStr := err.Error()
		if isTransactionNotFoundError(errStr) {
			return nil, NewBlockchainError(
				ErrCodeTransactionFailed,
				"transaction not found",
				ChainTRON,
				map[string]interface{}{"txID": txID},
			)
		}

		return nil, NewBlockchainError(
			ErrCodeNetworkError,
			"failed to get transaction info by ID",
			ChainTRON,
			map[string]interface{}{"txID": txID, "error": err.Error()},
		)
	}
	return txInfo, nil
}

// isTransactionNotFoundError checks if the error indicates a transaction not found
func isTransactionNotFoundError(errStr string) bool {
	errLower := strings.ToLower(errStr)
	return strings.Contains(errLower, "not found") ||
		strings.Contains(errLower, "transaction does not exist") ||
		strings.Contains(errLower, "no such transaction") ||
		strings.Contains(errLower, "invalid transaction") ||
		strings.Contains(errLower, "transaction not exist")
}

// IsTransactionNotFoundError checks if the error indicates a transaction not found (exported for external use)
func IsTransactionNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	return isTransactionNotFoundError(err.Error())
}

// sendTRXTransaction sends native TRX transaction
func (c *TRONClient) sendTRXTransaction(ctx context.Context, req *SendTransactionRequest) (string, error) {
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(req.PrivateKey, "0x"))
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"invalid private key format",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Convert amount to Sun (1 TRX = 10^6 Sun)
	amountSun := req.Amount.Mul(decimal.NewFromInt(1e6)).IntPart()

	// Create transfer transaction
	tx, err := c.client.Transfer(req.FromAddress, req.ToAddress, amountSun)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to create TRX transfer transaction",
			ChainTRON,
			map[string]interface{}{
				"from":   req.FromAddress,
				"to":     req.ToAddress,
				"amount": amountSun,
				"error":  err.Error(),
			},
		)
	}

	// Sign transaction using tron-wallet
	txSigned, err := tronwallet.SignTransaction(tx, privateKey)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to sign TRX transaction",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Broadcast transaction
	result, err := tronwallet.BroadcastTransaction(c.client, txSigned)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to broadcast TRX transaction",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	if !result.GetResult() {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"TRX transaction broadcast failed",
			ChainTRON,
			map[string]interface{}{"message": string(result.GetMessage())},
		)
	}

	// Get transaction hash
	txHash := hex.EncodeToString(txSigned.GetTxid())
	return txHash, nil
}

// sendTRC20Transaction sends TRC20 token transaction
func (c *TRONClient) sendTRC20Transaction(ctx context.Context, req *SendTransactionRequest) (string, error) {
	privateKey, err := crypto.HexToECDSA(strings.TrimPrefix(req.PrivateKey, "0x"))
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeInvalidPrivateKey,
			"invalid private key format",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// For TRC20, amount precision is usually 6 decimals for USDT
	amountInt := req.Amount.Mul(decimal.NewFromInt(1e6)).IntPart()

	// Create TRC20 transfer transaction
	tx, err := c.client.TRC20Send(req.FromAddress, req.ToAddress, req.ContractAddress, big.NewInt(amountInt), 100000000) // 100 TRX fee limit
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to create TRC20 transfer transaction",
			ChainTRON,
			map[string]interface{}{
				"from":     req.FromAddress,
				"to":       req.ToAddress,
				"contract": req.ContractAddress,
				"amount":   amountInt,
				"error":    err.Error(),
			},
		)
	}

	// Sign transaction using tron-wallet
	txSigned, err := tronwallet.SignTransaction(tx, privateKey)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to sign TRC20 transaction",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	// Broadcast transaction
	result, err := tronwallet.BroadcastTransaction(c.client, txSigned)
	if err != nil {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"failed to broadcast TRC20 transaction",
			ChainTRON,
			map[string]interface{}{"error": err.Error()},
		)
	}

	if !result.GetResult() {
		return "", NewBlockchainError(
			ErrCodeTransactionFailed,
			"TRC20 transaction broadcast failed",
			ChainTRON,
			map[string]interface{}{"message": string(result.GetMessage())},
		)
	}

	// Get transaction hash
	txHash := hex.EncodeToString(txSigned.GetTxid())
	return txHash, nil
}

// Close closes the TRON client
func (c *TRONClient) Close() error {
	if c.client != nil {
		c.client.Stop()
	}
	return nil
}

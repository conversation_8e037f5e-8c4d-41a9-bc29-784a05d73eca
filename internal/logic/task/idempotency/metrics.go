package idempotency

import (
	"sync"
	"sync/atomic"
)

// Metrics tracks idempotency-related metrics
type Metrics struct {
	// Lock acquisition metrics
	lockAcquiredTotal atomic.Uint64
	lockRejectedTotal atomic.Uint64
	redisErrorTotal   atomic.Uint64

	// Additional metrics for debugging
	activeLocksCount atomic.Int32
	
	// Mutex for thread-safe operations
	mu sync.RWMutex
}

// GlobalMetrics is the singleton instance for metrics
var GlobalMetrics = &Metrics{}

// IncrementLockAcquired increments the count of successfully acquired locks
func (m *Metrics) IncrementLockAcquired() {
	m.lockAcquiredTotal.Add(1)
	m.activeLocksCount.Add(1)
}

// IncrementLockRejected increments the count of rejected lock attempts
func (m *Metrics) IncrementLockRejected() {
	m.lockRejectedTotal.Add(1)
}

// IncrementRedisError increments the count of Redis errors
func (m *Metrics) IncrementRedisError() {
	m.redisErrorTotal.Add(1)
}

// DecrementActiveLocks decrements the active locks count (when TTL expires)
func (m *Metrics) DecrementActiveLocks() {
	m.activeLocksCount.Add(-1)
}

// GetStats returns current metrics statistics
func (m *Metrics) GetStats() MetricsStats {
	return MetricsStats{
		LockAcquiredTotal: m.lockAcquiredTotal.Load(),
		LockRejectedTotal: m.lockRejectedTotal.Load(),
		RedisErrorTotal:   m.redisErrorTotal.Load(),
		ActiveLocksCount:  int(m.activeLocksCount.Load()),
	}
}

// Reset resets all metrics (useful for testing)
func (m *Metrics) Reset() {
	m.lockAcquiredTotal.Store(0)
	m.lockRejectedTotal.Store(0)
	m.redisErrorTotal.Store(0)
	m.activeLocksCount.Store(0)
}

// MetricsStats represents a snapshot of metrics at a point in time
type MetricsStats struct {
	LockAcquiredTotal uint64 `json:"idempotency_lock_acquired_total"`
	LockRejectedTotal uint64 `json:"idempotency_lock_rejected_total"`
	RedisErrorTotal   uint64 `json:"idempotency_redis_error_total"`
	ActiveLocksCount  int    `json:"idempotency_active_locks_count"`
}

// GetEffectiveRate returns the percentage of rejected attempts (idempotency effectiveness)
func (s *MetricsStats) GetEffectiveRate() float64 {
	total := s.LockAcquiredTotal + s.LockRejectedTotal
	if total == 0 {
		return 0
	}
	return float64(s.LockRejectedTotal) / float64(total) * 100
}
grpc:
  client:
    user-service:
      endpoints:
      - "${TASK_WITHDRAW_GRPC_CLIENT_USER-SERVICE_ENDPOINTS_0}"
      apiKeys: "${TASK_WITHDRAW_GRPC_CLIENT_USER-SERVICE_APIKEYS}"
      balancer: "${TASK_WITHDRAW_GRPC_CLIENT_USER-SERVICE_BALANCER}"
      timeout: ${TASK_WITHDRAW_GRPC_CLIENT_USER-SERVICE_TIMEOUT}
redis:
  default:
    address: "${TASK_WITHDRAW_REDIS_DEFAULT_ADDRESS}"
    db: ${TASK_WITHDRAW_REDIS_DEFAULT_DB}
    pass: "${TASK_WITHDRAW_REDIS_DEFAULT_PASS}"
    idleTimeout: "${TASK_WITHDRAW_REDIS_DEFAULT_IDLETIMEOUT}"
logger:
  path: "${TASK_WITHDRAW_LOGGER_PATH}"
  level: "${TASK_WITHDRAW_LOGGER_LEVEL}"
  stdout: ${TASK_WITHDRAW_LOGGER_STDOUT}
  rotateSize: "${TASK_WITHDRAW_LOGGER_ROTATESIZE}"
  rotateExpire: "${TASK_WITHDRAW_LOGGER_ROTATEEXPIRE}"
  format: "${TASK_WITHDRAW_LOGGER_FORMAT}"
withdrawalProcessor:
  enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLED}
  spec: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_SPEC}"
  batchSize: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_BATCHSIZE}
  idempotency:
    enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_IDEMPOTENCY_ENABLED}
    lockTtl: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_IDEMPOTENCY_LOCKTTL}
    redisTimeout: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_IDEMPOTENCY_REDISTIMEOUT}
  transactionTypes:
    userWithdrawals:
      enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRANSACTIONTYPES_USERWITHDRAWALS_ENABLED}
      batchSize: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRANSACTIONTYPES_USERWITHDRAWALS_BATCHSIZE}
    merchantWithdrawals:
      enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRANSACTIONTYPES_MERCHANTWITHDRAWALS_ENABLED}
      batchSize: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRANSACTIONTYPES_MERCHANTWITHDRAWALS_BATCHSIZE}
    merchantSettlements:
      enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRANSACTIONTYPES_MERCHANTSETTLEMENTS_ENABLED}
      batchSize: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRANSACTIONTYPES_MERCHANTSETTLEMENTS_BATCHSIZE}
  trc20EnergyManagement:
    enabled: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ENABLED}
    bypassCheck: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_BYPASSCHECK}
    energyReservePercent: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ENERGYRESERVEPERCENT}
    orderTimeoutHours: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ORDERTIMEOUTHOURS}
    maxRetryCount: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_MAXRETRYCOUNT}
    checkIntervalSeconds: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_CHECKINTERVALSECONDS}
    itrx:
      apiKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APIKEY}"
      apiSecret: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APISECRET}"
      apiBaseUrl: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_APIBASEURL}"
      energyPeriod: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TRC20ENERGYMANAGEMENT_ITRX_ENERGYPERIOD}"
  enabledTokens:
    ETH: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_ETH}
    USDT_ERC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_USDT_ERC20}
    TRX: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_TRX}
    USDT_TRC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_ENABLEDTOKENS_USDT_TRC20}
  tokenContracts:
    USDT_ERC20: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENCONTRACTS_USDT_ERC20}"
    USDT_TRC20: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENCONTRACTS_USDT_TRC20}"
  tokenPrecisions:
    ETH: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_ETH}
    USDT_ERC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_USDT_ERC20}
    TRX: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_TRX}
    USDT_TRC20: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_TOKENPRECISIONS_USDT_TRC20}
  wallets:
    ETH:
      privateKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_PRIVATEKEY}"
      privateKeyEnvVar: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_PRIVATEKEYENVVAR}"
      address: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_ETH_ADDRESS}"
    TRON:
      privateKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_PRIVATEKEY}"
      privateKeyEnvVar: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_PRIVATEKEYENVVAR}"
      address: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_WALLETS_TRON_ADDRESS}"
  rpc:
    ETH:
      url: ''
      chainId: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_ETH_CHAINID}
    TRON:
      url: ''
      apiKey: "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_APIKEY}"
      callTimeoutSeconds: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RPC_TRON_CALLTIMEOUTSECONDS}
  retry:
    maxAttempts: ${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_MAXATTEMPTS}
    nonRetryableErrors:
    - "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_0}"
    - "${TASK_WITHDRAW_WITHDRAWALPROCESSOR_RETRY_NONRETRYABLEERRORS_1}"
withdrawalConsumer:
  enabled: ${TASK_WITHDRAW_WITHDRAWALCONSUMER_ENABLED}
  redisQueueName: "${TASK_WITHDRAW_WITHDRAWALCONSUMER_REDISQUEUENAME}"
  concurrency: ${TASK_WITHDRAW_WITHDRAWALCONSUMER_CONCURRENCY}
  dlqName: "${TASK_WITHDRAW_WITHDRAWALCONSUMER_DLQNAME}"
grpcUpdater:
  redisQueueName: "${TASK_WITHDRAW_GRPCUPDATER_REDISQUEUENAME}"
  redisDlqName: "${TASK_WITHDRAW_GRPCUPDATER_REDISDLQNAME}"
  brpopTimeoutSeconds: ${TASK_WITHDRAW_GRPCUPDATER_BRPOPTIMEOUTSECONDS}
  maxRetries: ${TASK_WITHDRAW_GRPCUPDATER_MAXRETRIES}
  retryDelaySeconds: ${TASK_WITHDRAW_GRPCUPDATER_RETRYDELAYSECONDS}
  merchantRedisQueueName: "${TASK_WITHDRAW_GRPCUPDATER_MERCHANTREDISQUEUENAME}"
  merchantDLQName: "${TASK_WITHDRAW_GRPCUPDATER_MERCHANTDLQNAME}"
  merchantTimeout: ${TASK_WITHDRAW_GRPCUPDATER_MERCHANTTIMEOUT}
  merchantMaxRetries: ${TASK_WITHDRAW_GRPCUPDATER_MERCHANTMAXRETRIES}
consul:
  address: "${TASK_WITHDRAW_CONSUL_ADDRESS}"
  token: "${TASK_WITHDRAW_CONSUL_TOKEN}"
  config_prefix: "${TASK_WITHDRAW_CONSUL_CONFIG_PREFIX}"
